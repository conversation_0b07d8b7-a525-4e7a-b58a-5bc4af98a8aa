////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetMesh.h
//! <AUTHOR>
//! @brief 重叠网格功能。
//! @date 2021-11-30
//
//------------------------------修改日志----------------------------------------
// 2021-11-30 曾凯
//    说明：创建，初步建立重叠模块程序框架
// 2021-12-15 曾凯
//    说明：（1）基于KDT算法进行壁面距离计算和贡献单元搜索，对贡献单元进行二次筛选；
//         （2）基于贡献单元搜索结果和壁面距离实现自动化的重叠网格类型判定，挖洞效果良好；
//   以上记为重叠模块V1版本；
// 2021-12-30 曾凯
//    说明：（1）将壁面距离计算从计算当前单元到所有子域壁面的距离，修改为d仅计算到自身子域壁面的距离；
//			（2）挖洞时从对比当前单元到所有子域壁面的距离，修改为对比该单元到自身壁面的距离与贡献单元到自身壁面的距离；
//          (3)增加对背景网格不存在壁面的情况的处理，将背景网格的壁面距离设为极大值
//          (4)将创建网格单元KDT树的方式由原来的以dim空间的网格中心为标准，修改为以2*dim空间的以网格单元上下界为标准，搜索时以当前单元的中心构建2*dim搜索域，修正了个别网格找不到贡献单元的问题；
// 2022-05-01 曾凯
//    说明：(1) 移植到2022年初的新框架
//		   （2）修改为并行重叠装配
//         （3）将装配策略从贡献单元优先修改为壁面距离优先
// 2023-12-15 曾凯
//    说明：(1) 与2023年1.1.5.4版本合并，正式融入主版本流程，后续更改由Git记录
//		   （2）对Bug进行了修改，对多个二维三维算例进行了测试
//         （3）初步实现动态装配
// 2024-02-28 曾凯
//     说明：对装配流程及并行策略进行大幅改进，尚未完工，后续程序修改转入GitLab平台监控，不再写入此处
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetMesh_
#define _specialModule_oversetMesh_OversetMesh_


#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "feilian-specialmodule/oversetMesh/Donor.h"
#include "meshProcess/wallDistance/WallDistanceManager.h"

#include "sourceFlow/package/FlowPackage.h"
#include "meshProcess/zone/ZoneManager.h"
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/mpi.hpp" // linshi
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/optional/optional.hpp"

namespace Overset
{
class OversetMesh
{ 
public:
	/**
	 * @brief Construct a new Overset Mesh object
	 *
	 * @param flowPackageVector_
	 */
	OversetMesh(Package::FlowPackage &flowPackage_);
	
	/**
	 * @brief Destroy the Overset Mesh object
	 * 
	 */
    ~OversetMesh();

	/**
	 * @brief 初始化重叠网格装配，Initialize Overset Grid Assembly
	 * 
	 */
    void InitOGA();

	/**
	* @brief 基于已有装配信息进行网格装配更新，以尽量减小装配时间
	*/
	void UpdateOGA();

	/**
	* @brief 更新重叠网格单元类型场
	*/
	void UpdateOversetField();

private:
	/**
	* @brief 初始化设定成员变量
	*/
	void Initialize();

	/**
	* @brief 检查参数、重叠网格等输入是否正确
	*/
	void CheckInputs();

	/**
	* @brief 创建点相邻单元数据结构，用于贡献单元列表的扩充
	*/
	void CreateNodeNeiElem();
	
	//
	// *****************************重叠网格装配相关***********************************
	//
	/**
	* @brief 将包围盒范围最大的子域判定为背景网格
	*/
	void FindBackgroundZone();

	/**
	* @brief 判断网格单元的重叠类型
	*/
	void JudgeElemType();

	/**
	* @brief 直接基于网格单元的指标，判断其重叠类型
	*/
	void JudgeElemTypeElemBased();

	/**
	* @brief 基于网格点的指标判断其重叠类型, 最短壁面距离为自身子域时为计算节点，其他为非计算节点
	*/
	void JudgeNodeType();

	/**
	* @brief 基于网格点的类型进一步判断网格单元的类型
	*/
	void JudgeElemTypeNodeBased();

	/**
	* @brief 基于初始挖洞，将与计算单元相邻的洞单元设为初始插值单元阵面
	*/
	void CreateInitialFringe(Set<Acceptor> &initialFringe);

	/**
	* @brief 对初始挖洞后的网格进行重叠插值区域推进，可根据设置推进不同层数
	*/
	void AdvanceFringe();

	/**
	* @brief 将与重叠边界相邻的计算单元修改为插值单元
	*/
	void MarkElemAtOversetBoundary(Set<int> &fringeElemID);

	/**
	 * @brief 将stagedAcceptor向洞单元区域推进一层
	 * 
	 * @param stagedAcpts 暂存的Acceptor
	 * @param neiDonorSearchResults 相邻单元的贡献单元搜索结果
	 */
	void ForwardAdvance(Set<Acceptor> &stagedAcpts, Set<Acceptor> &neiDonorSearchResults);

	/**
	 * @brief 将当前插值边界整体推进一层
	 * 
	 * @param neiAcpts 
	 */
	void AdvanceOneLayer(Set<Acceptor> &neiLayerAcpts);

	/**
	 * @brief 将stagedAcceptor向计算单元区域推进一层
	 * 
	 * @param stagedAcpts 
	 * @param neiDonorSearchResults 
	 */
	void BackwardAdvance(Set<Acceptor> &stagedAcpts, Set<Acceptor> &neiDonorSearchResults);

	/**
	 * @brief 将一个Acceptor对象放入暂存列表
	 * 
	 * @param elemID 
	 */
	void StageOneAcceptor(const int &elemID);

	/**
	 * @brief 将一个Acceptor对象加入暂存列表，属于本进程的加入realList,并行虚单元的加入ghostList
	 * 
	 * @param acpt 
	 * @param realList
	 * @param ghostList 
	 */
	void StageOneAcceptor(const Acceptor &acpt, Set<Acceptor> &realList, List<Acceptor> &ghostList);
	
	/**
	 * @brief 将一个Acceptor对象加入确认列表，第一层按贡献单元所在进程编号排列
	 * 
	 * @param acpt 
	 * @param commitAcptList 
	 */
	void CommitOneAcceptor(Acceptor &acpt, List<List<Acceptor>> &commitAcptList);
	
	/**
	 * @brief 修改传入的插值单元列表的重叠类型场值为插值单元
	 * 
	 * @param acpts 
	 */
	void SetAcceptorFieldValue(Set<Acceptor> &acpts);


	/**
	 * @brief 修改传入的单元编号列表的重叠类型场值为插值单元
	 * 
	 * @param elemIDlist 
	 */
	void SetAcceptorFieldValue(Set<int> &elemIDlist);

	/**
	 * @brief 优化初始边界：
	 * 		1.移除虚单元
	 * 		2.将冗余的插值单元（即不与任何洞单元点相邻）修改为计算单元
	 * 		3.将无法搜索到贡献单元的插值单元向计算单元回退一层
	 * @param
	 */
	void OptimizeInitialFringe();

	/**
	 * @brief 从给定的Acceptor列表中，将冗余的插值单元（即不与任何洞单元点相邻）修改为计算单元
	 * 
	 * @param acpts 
	 */
	void ModifyRedundantAcceptor(Set<Acceptor> &acpts);

	
	/**
	 * @brief  判断当前Acpt是否向给定的相邻层进行推进
	 * 
	 * @param currentAcpt 当前插值单元
	 * @param neiHoleAcpts 相邻层插值单元列表
	 * @return true 
	 * @return false 
	 */
	bool JudgeAdvance(const Acceptor &currentAcpt, const List<Acceptor> &neiAcpts);

	bool JudgeAdvanceBackward(const Acceptor &currentAcpt, const List<Acceptor> &neiAcpts);

	//
	// ************************壁面距离计算相关************************************
	//
	/**
	* @brief 基于KDT树计算壁面距离
	*/
	void CalculateWallDistByKDT();

	/**
	* @brief 计算各子域单元中心到各子域壁面的最短距离，保存到wallDistances中
	*/
	void CalculateWallDistances();

	/**
	* @brief 更新网格中的壁面距离容器
	*/
	void UpdateWallDistField();

	/**
	* @brief 判断指定网格单元的最短壁面距离是否是自身所在子域，是返回true，否为false
	*/
	bool IsNearestWallDistToSelf(int &elemID, int &elemZoneID);

	/**
	 * @brief 对计算出的壁面距离进行法向修正
	 *  
	 */
	void CorrectWallDist();

	//
	// ********************并行相关**************
	/**
	 * @brief 
	 * 
	 * @tparam T 
	 * @param srcList 待合并列表
	 */
	template <typename T>
	void AllGatherAndMergeList(List<T> &srcList);

	/**
	 * @brief 将待搜索的Acceptor收集到可能存在贡献单元的进程中，后续由相应进程负责贡献单元搜索
	 *
	 * @param[in] srcAcceptors 待搜索的Acceptor列表,该数据需已经用GroupingAcceptors函数进行了分组
	   @param[out] collectedAcceptors 从各进程收集来的需要在本进程进行搜索的Acceptor，按照其来源进程放置
	 */
	void CollectAcceptors(const List<List<Acceptor>> &srcAcceptors,
						  List<List<Acceptor>> &collectedAcceptors);

	/**
	 * @brief 从其他进程取回搜索后的Acceptor列表，并合并为一个列表容器
	 *
	 * @param[in] collectedAcceptors 本进程对所有进程的搜索结果
	   @param[out] searchResults 属于当前进程的所有Acceptor的搜索结果
	 */
	void CollectAcceptorsReverse(const List<List<Acceptor>> &collectedAcceptors,
						  List<Acceptor> &searchResults);


	//
	// 贡献单元搜索相关
	//
	/**
	* @brief 计算网格单元的上下界包围盒(AABB, Axis align Bounding Box)
	*/
	void ComputeElemBoundBox();

	/**
	* @brief 将网格单元的AABB按照所属子域放入点云，并创建KDT树
	*/
	void CreateElemKdtTrees();

	/**
	* @brief 将KDT树AABB的上下界同步至所有进程
	*/
	void AllGatherTreeInfo();

	/**
	* @brief 为本进程的批量单元编号在全进程范围内并行搜索贡献单元，搜索结果按照搜索的进程编号存放在searchResults中
	* @param queryElemIDm 单元编号set集合
	*/
	void ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults);

	/**
	 * @brief 为传入的Acceptor列表搜索贡献单元
	 * 
	 */
	void ChunkDonorSearch(List<Acceptor> &acptList);

	/**
	* @brief 判断指定的elem是否有可能与目标树相交，以决定是否进入该树搜索贡献单元
	*/
	bool ElemCenterInTree(const int &elemID, const TreeInfo &treeInfo);

	/**
	 * @brief 给定一个点，在当前进程网格中搜索其贡献单元
	 * 
	 * @param[in] srcNode 插值点坐标
	 * @param[in] tgtTree 用于搜索的kdt树
	 * @return int 贡献单元编号，未找到时返回-1
	 */
	int DonorSearch(const Node &srcNode, KdtTree *tgtTree);

	/**
	 * @brief 将插值单元按照可能存在贡献单元的进程分组，以便后续进行并行搜索
	 * 		  一个插值单元可能到多个进程中搜索
	 *
	 * @param[in] searchElemID 需要搜索贡献单元的编号
	 * @param[out] groupedAcceptors 分组结果，并创建Acceptor对象,放入可能存在贡献单元的进程编号中
	 */
	void GroupingAcceptors(Set<int> &searchElemID,
						  List<List<Acceptor>> &groupedAcceptors);

	/**
	 * @brief 为某个Acceptor对象检查各个进程的搜索结果中是否有贡献单元信息，有多个搜索结果时选择最优的
	 * 
	 * @param searchResults 搜索结果
	 */
	void SelectBestDonor(Acceptor &srcAcpt, const List<List<Acceptor>> &tgtResults);

	/**
	 * @brief 合并各个进程的搜索结果，去除重复元素（选择最优贡献单元）
	 * 
	 * @param groupedSearchResults 
	 */
	void SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults);

	/**
	 * @brief 将真实维度（dim =2,3）的坐标点转换为2*dim空间的范围，以在2*dim空间的网格单元KDT树中进行范围搜索
	 * 
	 * @param[in] node 坐标点
	 * @param[] rangeMin2dim 
	 * @param rangeMax2dim 
	 */
	void NodeTo2dimRange(const Node &node, List<Scalar> &rangeMin2dim, List<Scalar> &rangeMax2dim);

	/**
	 * @brief 根据已经确认完毕的Acceptor列表，搜集当前进程中作为贡献单元的信息，并创建Donor对象
	 * 
	 */
	void CreateCommitedDonors();

	/**
	 * @brief 从目标贡献单元搜索结果列表中找到指定单元的信息
	 * 
	 * @param elemID 
	 * @param tgtResults 
	 * @return Acceptor& 
	 */
	const Acceptor & GetDonorSearchResults(const int &elemID, const Set<Acceptor> &tgtResults);

	/**
	 * @brief 对最终的插值边界进行检测和改进：
	 * 			1. 对无贡献单元的进行报错
	 * 			2. 对贡献单元的重叠类型为Acceptor的，进行警示但可以继续
	 * 			3. 对贡献单元的重叠类型为Hole的，用相邻贡献单元较优的代替
	 * 			
	 */
	void CheckAndImproveFinalFringe(Set<Acceptor> &finalFringe);

	/**
	 * @brief 对贡献单元为Hole的插值单元进行改进
	 * 			
	 */
	void ImproveWorstDonorQuality(const Acceptor &acpt);


	void NonBlockingDonorSearch(); //弃用


	//
	// 插值方法相关 
	//
	/**
	 * @brief 根据插值方法为Donor对象填充完整贡献单元和权重
	 * 
	 * @param  
	 */
	void FillDonorAndWeights();

	/**
	 * @brief 反距离加权插值方法的贡献单元填充及权重计算
	 * 
	 */
	void InverseDistance();

	/**
	 * @brief 反距离加权权重计算
	 * 
	 * @param tgtNode 插值目标点坐标
	 * @param srcElemID 贡献单元编号
	 * @param weights 权重值
	 */
	void CalcInverseDistanceWeights(const Node &tgtNode,
									const List<int> &srcElemID,
									List<Scalar> &weights);

	//
	// 基本工具
	//
	/**
	 * @brief 收集指定类型的边界的全局面元至指定容器，按子域顺序排列
	 */
	void CollectGlobalBCfaces(List<List<Face>> &globalFaces, List<List<List<Vector>>> &globalFaceNodes, const Boundary::Type &bcType);

	/**
	 * @brief 传入一个面元容器，创建KDT树
	 */
	KdtTree *CreateFaceKdtTree(const List<Face> &faceList);

	/**
	* @brief 判断点是否在重叠边界内侧
	*/
	bool NodeInOversetBC(const Vector &node);

	/**
	 * @brief 判断坐标点是否在网格单元内部
	 * 
	 * @param node 坐标点
	 * @param elem 网格单元
	 * @return true 
	 * @return false 
	 */
	bool NodeInElem(const Node &node, const Element &elem);

	/**
	* @brief 判断点是否在物面内部
	*/
	bool NodeInWall(const int &nodeID, const int &zoneID);

	/**
	 * @brief 判断点是否在某一网格面内部
	 * 
	 * @param node 目标点
	 * @param face 目标面
	 * @param faceNodes 构成面的点坐标
	 * @return true 
	 * @return false 
	 */
	bool NodeInFace(const Node &node, const Face &face, const List<Vector> &faceNodes);

	/**
	 * @brief 给定一个单元编号，找到所有点相邻单元编号
	 * 
	 * @param elemID 
	 * @param nodeNeiID 
	 */
	void GetNodeNeighbour(const int &elemID, Set<int> &nodeNeiID);

	/**
	 * @brief 给定一个单元编号，找到所有点相邻、且为计算单元的相邻单元编号
	 * 
	 * @param elemID 
	 * @param nodeNeiCalc 
	 */
	void GetNodeNeighbourCalc(const int &elemID, List<int> &nodeNeiCalc);

	/**
	 * @brief 给定一组插值单元，找到所有点相邻、且为计算单元的相邻单元编号
	 * 
	 * @param srcAcpt 
	 * @param nodeNeiCalc 
	 */
	void GetNodeNeighbourCalc(const Set<Acceptor> &srcAcpt, Set<int> &nodeNeiCalc);

		/**
	 * @brief 给定一个单元编号，找到所有点相邻、且为洞单元的相邻单元编号
	 *
	 * @param acpt
	 * @param nodeNeiHole
	 */
	void GetNodeNeighbourHole(const int &elemID, List<int> &nodeNeiHole);
	
	/**
	 * @brief 为批量的Acceptor找到点相邻的洞单元编号
	 * 
	 * @param srcAcpt 
	 * @param nodeNeiHole
	 */
	void GetNodeNeighbourHole(const Set<Acceptor> &srcAcpt, Set<int> &nodeNeiHole);

	/**
	 * @brief 给定一个面，找到与其点相邻的所有单元编号
	 * 
	 * @param face 
	 * @param neiIDlist 
	 */
	void GetNodeNeighbour(const Face &face, Set<int> &nodeNei);

	/**
	 * @brief 给定一个面，找到与其点相邻的洞单元编号
	 * 
	 * @param face 
	 * @param neiIDlist 
	 */
	void GetNodeNeighbourHole(const Face &face, Set<int> &nodeNeiHole);

	/**
	 * @brief 给定一个面，找到与其点相邻的计算单元编号
	 * 
	 * @param face 
	 * @param neiIDlist 
	 */
	void GetNodeNeighbourCalc(const Face &face, Set<int> &nodeNeiCalc);

	/**
	 * @brief 给定一个单元编号，找到所有与其点相邻的插值单元编号
	 * 
	 * @param elemID 
	 * @param nodeNeiAcpt 
	 */
	void GetNodeNeighbourAcpt(const int &elemID, Set<int> &nodeNeiAcpt);

	/**
	 * @brief 为指定的单元编号获取所有面相邻单元编号
	 * 
	 * @param elemID 
	 * @param neiIDlist 
	 */
	void GetFaceNeighbour(const int &elemID, List<int> &neiIDlist);

	void GetFaceNeighbourCalc(const int &elemID, List<int> &calcNeiID);
	void GetFaceNeighbourAcpt(const int &elemID, List<int> &acptNeiID);

	/**
	 * @brief 为单个Acceptor找到相邻的洞单元编号
	 *
	 * @param acpt
	 * @param holeNeiID
	 */
	void GetFaceNeighbourHole(const Acceptor &srcAcpt, List<int> &holeNeiID);

	/**
	 * @brief 获取与指定面相邻的洞单元
	 * 
	 * @param face 
	 * @param holeNeiID 
	 */
	void GetFaceNeighbourHole(const Face &face, Set<int> &holeNeiID);

	/**
	 * @brief 为批量的Acceptor找到相邻的洞单元编号，Set容器可保持编号的唯一性
	 * 
	 * @param acptList 
	 * @param holeNeiID 
	 */
	void GetFaceNeighbourHole(const Set<Acceptor> &srcAcpt, Set<int> &neiHoleAcpt);

	/**
	 * @brief 将与重叠边界点相邻的单元编号放入列表
	 * 
	 */
	void GetOversetPatchElemID();

	/**
	 * @brief 判断给定单元是否与重叠边界点相邻
	 * 
	 */
	bool JudgeOversetPatchElem(const int &elemID);

	/**
	 * @brief 将Acceptor根据贡献单元的进程号进行分组
	 * 
	 */
	void GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts);

	/**
	 * @brief 将重叠装配结果至Mesh中
	 * 
	 */
	void UpdateElemTypeFieldToMesh(ElementField<int> *field, Mesh *mesh);

	/**
	 * @brief 将装配完成后的细网格单元类型传递到各层粗网格上
	 * 
	 */
	void UpdateElementTypeToMultiGrid();

	/**
	 * @brief 判断点n1、n2组成的线段是否与面f所在的无限平面相交
	 * 
	 */
	bool isSegmentIntersectPlane(const Node &n1, const Node &n2, const Face &f);

	/**
	 * @brief 判断点n1、n2组成的线段是否与有限多边形面f相交
	 * 
	 */
	bool isSegmentIntersectPolygon(const Node &n1, const Node &n2, const Face &f);

	/**
	 * @brief 判断同一平面中点n是否在面f内部
	 * 		  基于射线法
	 * 
	 */
	bool isNodeInPolygon(const Node &n, const List<Node> &polygon);






	//初始化重叠网格相关的流场变量场
	void InitOverFields();

	//读取旧的重叠信息
	void ReadOldOverFields();




	//找到处于并行边界上的单元在其他进程的相邻单元编号，并返回进程编号和当地单元编号
	void FindParallelNeighbour(int &neiProcID, int &neiElemID);

	//从overElemDonorStencil中删除贡献单元信息
	void DeletDonorStencil(int elemID);

	/**
	* @brief 读入已有的网格装配插值关系，暂不需要
	*/
	void ReadOversetConnectivity();

	/**
	* @brief 输出最新的网格装配插值关系，暂不需要
	*/
	void WriteOversetConnectivity();

private:
	Package::FlowPackage &flowPackage;
    Mesh *localMesh; //当前进程网格指针
	SubMesh *subMesh; //当前进程SubMesh指针
	const Configure::Flow::FlowConfigure &flowConfig;
	
	ZoneManager *zoneManager; //域信息管理器

	int n_Zones;                     //网格子域个数
	int myZoneID;                    //当前进程网格所在子域编号
	Mesh::MeshDim dim;               //网格维度
	List<List<int>> nodeNeiElem;     //各网格点的相邻单元编号，用于推进插值边界及扩展贡献单元列表，不包含各种虚单元

	// 当前进程作为插值单元的列表, 最终确认的插值单元
	List<List<Acceptor>> &commitedAcceptors; 
	// 当前进程作为贡献单元的列表
	List<List<Donor>> &commitedDonors;
	
	// 当前进程暂存的插值单元列表，这些插值单元均需要进一步推进确认
	Set<Acceptor> stagedAcpts;
	Set<Acceptor> forwardStagedAcpts; // 前向推进的暂存容器，舍弃
	Set<Acceptor> backwardStagedAcpts; // 后向推进的暂存容器，舍弃
	Set<Acceptor> donorSearchResults;

	//
	// 重叠装配相关
	//   
	int localOversetPatchID; // 重叠边界的当地编号
	Set<int> oversetPatchElemID; // 与重叠边界相邻的单元编号
	const OversetType::InterpolationType &interpolationType; //重叠边界插值方法
	ElementField<int> *elemTypeField; //网格单元重叠类型场
	// Map<int, DonorInfo> elemDonorStencil;
	int BGzoneID; //背景网格子域编号
	List<List<Face>> globalOversetFaces; // 全局重叠边界面元，按子域编号排列，用于判断单元是否在重叠边界内
	List<List<List<Vector>>> globalOversetFaceNodes; // 全局壁面面元对应的网格点坐，与globalOversetFaces中的面元一一对应
	List<KdtTree *> globalOversetFaceTrees; //全局重叠边界kdt树

	List<int> nodeOversetType; // 网格点的重叠类型
	// Map<int, DonorInfo> nodeDonorStencil; //网格点的贡献单元信息
	int maxAdvanceNum;                      //插值边界最大推进层数
	int minAdvanceNum;						//插值边界最小推进层数
	int advanceNum;							//插值边界当前推进层数
	int interpolationLayerNum;                //插值单元保留层数，interpolationLayerNum <= advanceLayerNum
	bool verboseInfo;                         //重叠模块
	AssembleMethod assembleMethod; // 网格装配方法
	List<int> acceptorDonorFlag; // 用于标记插值单元是否被用作了贡献单元，0：否；1：是 

	//
	// 壁面距离计算相关
	//
	List<List<Face>> globalWallFaces; // 全局壁面面元，按子域编号排列
	List<List<List<Vector>>> globalWallFaceNodes; // 全局壁面面元对应的网格点坐，与globalWallFaces中的面元一一对应
	List<KdtTree *> globalWallFaceKdtTrees;  //各子域壁面面元的KDT树，按子域编号排列
	Turbulence::WallDistance wallDistMethod; // 壁面距离计算方法
	// 网格点或单元到各子域壁面的最短距离列表
	List<List<Scalar>> wallDistances;
	List<List<int>> nearestWallFaceID;

	//
	// 并行相关
	//
	boost::mpi::communicator mpi_world;
	int processorID;
	int nProcessor;

	bool listenFlag; // 非阻塞搜索监听启动标志
	
	//
	// 网格单元的KD树相关
	//
	List<List<Scalar>> elemBound; //网格单元的上下界的2dim容器，前一半为下界，后一半为上届

	//当前进程网格单元的KDT树容器，单个进程的网格可以包含多个域，按照子域编号排列
	List<KdtTree *> elemKdtTrees;

	// KDT树的全局信息，用于并行搜索时先确定发送目标
	List<TreeInfo> globalTreeInfo;
};
}
#endif