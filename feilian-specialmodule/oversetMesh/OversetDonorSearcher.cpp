#include "feilian-specialmodule/oversetMesh/OversetDonorSearcher.h"

namespace Overset
{

    OversetDonorSearcher::OversetDonorSearcher(Mesh *mesh_,
                                               ZoneManager *zoneManager_,
                                               ElementField<int> *elemTypeField_,
                                               const boost::mpi::communicator &mpi_world_)
        : localMesh(mesh_), zoneManager(zoneManager_), elemTypeField(elemTypeField_), mpi_world(mpi_world_), processorID(mpi_world_.rank()), nProcessor(mpi_world_.size())
    {
        n_Zones = zoneManager->GetZoneNumber();
        dim = localMesh->GetMeshDim();
    }

    OversetDonorSearcher::~OversetDonorSearcher()
    {
        Clear();
    }

    void OversetDonorSearcher::Initialize()
    {
        // 清理之前的数据
        Clear();

        // 计算网格单元包围盒
        ComputeElemBoundBox();

        // 创建KDT树
        CreateElemKdtTrees();

        // 同步全局树信息
        AllGatherTreeInfo();

        // 初始化贡献单元标记
        acceptorDonorFlag.assign(localMesh->GetElementNumberReal(), 0);
    }

    void OversetDonorSearcher::ComputeElemBoundBox()
    {
        const int elemNum = localMesh->GetElementNumberReal();

        elemBound.clear();
        elemBound.resize(elemNum);

        // 获取所有单元的上下界，成为2*dim空间中的点
        for (int elemID = 0; elemID < elemNum; elemID++)
        {
            elemBound[elemID].resize(2 * dim);
            for (int i = 0; i < dim; i++)
            {
                elemBound[elemID][i] = INF;
                elemBound[elemID][i + dim] = -1 * INF;
            }

            // 循环elem的点，找到其上下界
            for (int nodeI = 0; nodeI < localMesh->GetElement(elemID).GetNodeSize(); nodeI++)
            {
                const int &nodeID = localMesh->GetElement(elemID).GetNodeID(nodeI);
                const Node &tempNode = localMesh->v_node[nodeID];
                List<Scalar> temp = {tempNode.X(), tempNode.Y(), tempNode.Z()};

                for (int i = 0; i < dim; i++)
                {
                    elemBound[elemID][i] = std::min(elemBound[elemID][i], temp[i]);
                    elemBound[elemID][i + dim] = std::max(elemBound[elemID][i + dim], temp[i]);
                }
            }
        }
    }

    void OversetDonorSearcher::CreateElemKdtTrees()
    {
        elemKdtTrees.resize(n_Zones);

        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            int zoneElemStartID = zoneManager->GetZoneStartElemID(zoneID);
            int zoneElemNum = zoneManager->GetZoneElemNum(zoneID);

            if (zoneElemNum > 0)
            {
                // 将当前子域网格单元的AABB放入点云，将单元编号放入数据云
                List<List<Scalar>> pointCloud;
                List<int> dataCloud;
                for (int elemID = zoneElemStartID; elemID < zoneElemStartID + zoneElemNum; elemID++)
                {
                    pointCloud.push_back(elemBound[elemID]);
                    dataCloud.push_back(elemID);
                }
                elemKdtTrees[zoneID] = new KdtTree(2 * dim, pointCloud, dataCloud);

                pointCloud.clear();
                dataCloud.clear();
            }
            else // 子域在当前进程无网格，为空
            {
                elemKdtTrees[zoneID] = nullptr;
            }
        }
    }

    void OversetDonorSearcher::AllGatherTreeInfo()
    {
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            if (elemKdtTrees[zoneID])
            {
                TreeInfo temp = TreeInfo();
                temp.procID = processorID;
                temp.zoneID = zoneID;
                temp.spaceMax = elemKdtTrees[zoneID]->GetMax();
                temp.spaceMin = elemKdtTrees[zoneID]->GetMin();

                globalTreeInfo.push_back(temp);
            }
        }

        // 并行时合并全局信息
        if (nProcessor > 1)
        {
            // 这里需要实现AllGatherAndMergeList功能
            // 暂时留作接口，在实际实现中需要进行MPI通信
            FatalError("OversetDonorSearcher::AllGatherTreeInfo() 需要实现MPI通信逻辑");
        }
    }

    bool OversetDonorSearcher::ElemCenterInTree(const int &elemID, const TreeInfo &treeInfo)
    {
        const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
        List<Scalar> center = {elemCenter.X(), elemCenter.Y(), elemCenter.Z()};
        for (int i = 0; i < dim; i++)
        {
            if (center[i] < treeInfo.spaceMin[i] || center[i] > treeInfo.spaceMax[i + dim])
            {
                return false;
            }
        }
        return true;
    }

    void OversetDonorSearcher::GroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcpts)
    {
        groupedAcpts.clear();
        groupedAcpts.resize(nProcessor);

        for (auto it = searchElemID.begin(); it != searchElemID.end(); it++)
        {
            int elemID = *it;
            int elemZoneID = zoneManager->GetElemZoneID(elemID);

            bool grouped = false;
            for (int treeI = 0; treeI < globalTreeInfo.size(); treeI++)
            {
                TreeInfo &treeInfo = globalTreeInfo[treeI];

                if (elemZoneID != treeInfo.zoneID) // 只搜索其他子域
                {
                    // 判断单元是否与该树空间相交
                    if (ElemCenterInTree(elemID, treeInfo))
                    {
                        const Node &center = this->localMesh->GetElement(elemID).GetCenter();
                        Acceptor temp(elemID, processorID, elemZoneID, center);
                        groupedAcpts[treeInfo.procID].push_back(temp);
                        grouped = true;
                    }
                }
            }

            if (grouped == false) // 无法分配到任何树（无贡献单元），加入到当前进程
            {
                const Node &center = this->localMesh->GetElement(elemID).GetCenter();
                Acceptor temp(elemID, processorID, elemZoneID, center);
                groupedAcpts[processorID].push_back(temp);
            }
        }
        searchElemID.clear();
    }

    void OversetDonorSearcher::Clear()
    {
        // 清理KDT树
        for (int zoneID = 0; zoneID < elemKdtTrees.size(); zoneID++)
        {
            delete elemKdtTrees[zoneID];
        }
        elemKdtTrees.clear();

        // 清理数据容器
        elemBound.clear();
        globalTreeInfo.clear();
        acceptorDonorFlag.clear();
    }

    void OversetDonorSearcher::ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults)
    {
        // 简化的并行搜索实现
        // 处理本进程内的搜索
        ChunkDonorSearch(groupedAcpts[processorID]);

        // 将本进程的搜索结果加入结果集
        searchResults.insert(groupedAcpts[processorID].begin(), groupedAcpts[processorID].end());

        // 实际的并行实现需要包含MPI通信逻辑
        // 这里暂时只处理本进程的搜索，避免程序崩溃
        if (nProcessor > 1)
        {
            // TODO: 实现完整的MPI并行搜索逻辑
            // 包括：发送搜索请求、接收搜索结果、合并结果等
        }
    }

    void OversetDonorSearcher::ChunkDonorSearch(List<Acceptor> &acptList)
    {
        if (acptList.size() == 0)
        {
            return;
        }

        for (int i = 0; i < acptList.size(); i++)
        {
            Acceptor &acpt = acptList[i];
            const Node &elemCenter = acpt.GetAcceptorCenter();
            const int &donorID = acpt.GetCentralDonorID();
            const int &donorProcID = acpt.GetCentralDonorProcID();

            if (donorID >= 0 && donorProcID == processorID) // 如果已经有贡献单元且就在本进程
            {
                const Element &elem = this->localMesh->GetElement(donorID);
                if (this->NodeInElem(elemCenter, elem)) // 且插值单元中心仍然在原来的贡献单元里面
                {
                    const int &newDonorType = elemTypeField->GetValue(donorID);
                    const Scalar &newDonorVolume = elem.GetVolume();
                    acpt.SetCentralDonor(donorID, donorProcID, newDonorVolume, newDonorType);
                    continue;
                }
            }

            // 在各个子域的KDT树中搜索贡献单元
            for (int zoneI = 0; zoneI < elemKdtTrees.size(); zoneI++)
            {
                if (elemKdtTrees[zoneI] && acpt.GetAcceptorZoneID() != zoneI) // 仅搜索其他子域的树
                {
                    int newDonorID = this->DonorSearch(elemCenter, elemKdtTrees[zoneI]);
                    if (newDonorID >= 0)
                    {
                        Scalar elemVolume = localMesh->GetElement(newDonorID).GetVolume();
                        acpt.SetCentralDonor(newDonorID,
                                             processorID,
                                             elemVolume,
                                             elemTypeField->GetValue(newDonorID));
                    }
                }
            }
        }
    }

    int OversetDonorSearcher::DonorSearch(const Node &srcNode, KdtTree *tgtTree)
    {
        int donorID = -1;

        // 确定2dim空间搜索范围
        List<Scalar> searchRangeMin;
        List<Scalar> searchRangeMax;
        this->NodeTo2dimRange(srcNode, searchRangeMin, searchRangeMax);

        // 初筛所有可能的相交单元
        List<KdtNode *> kdtNodeList;
        tgtTree->FindNodesInRegion(searchRangeMin, searchRangeMax, kdtNodeList);

        // 二次筛选，找到srcNode所在的贡献单元
        if (kdtNodeList.size() > 0)
        {
            for (int i = 0; i < kdtNodeList.size(); i++)
            {
                int &elemID = kdtNodeList[i]->data; // 获取单元编号
                const Element &elem = localMesh->GetElement(elemID);

                // 不对贡献单元的重叠类型进行判断，只要落在单元内即可作为贡献单元
                if (this->NodeInElem(srcNode, elem))
                {
                    donorID = elemID;
                    const int &elemType = elemTypeField->GetValue(elemID);
                    if (elemType == OversetElemType::ACCEPTOR) // 贡献单元作为了插值单元，标记出来
                    {
                        acceptorDonorFlag[elemID] = 1;
                    }
                    break; // 找到第一个合适的贡献单元即可
                }
            }
        }

        return donorID;
    }

    void OversetDonorSearcher::GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts)
    {
        groupedAcpts.clear();
        groupedAcpts.resize(nProcessor);

        for (auto it = srcAcpts.begin(); it != srcAcpts.end(); it++)
        {
            const int &donorProcID = it->GetCentralDonorProcID();
            const int &donorID = it->GetCentralDonorID();

            if (donorID >= 0)
            {
                groupedAcpts[donorProcID].push_back(*it);
            }
            else
            {
                int elemID = it->GetAcceptorID();
                int elemZoneID = zoneManager->GetElemZoneID(elemID);

                bool grouped = false;
                for (int treeI = 0; treeI < globalTreeInfo.size(); treeI++)
                {
                    TreeInfo &treeInfo = globalTreeInfo[treeI];

                    if (elemZoneID != treeInfo.zoneID) // 只搜索其他子域
                    {
                        // 判断单元是否与该树空间相交
                        if (ElemCenterInTree(elemID, treeInfo))
                        {
                            groupedAcpts[treeInfo.procID].push_back(*it);
                            grouped = true;
                        }
                    }
                }

                if (grouped == false) // 无法分配到任何树（无贡献单元），加入到当前进程
                {
                    groupedAcpts[processorID].push_back(*it);
                }
            }
        }
    }

    void OversetDonorSearcher::NodeTo2dimRange(const Node &node, List<Scalar> &rangeMin2dim, List<Scalar> &rangeMax2dim)
    {
        rangeMin2dim.clear();
        rangeMax2dim.clear();
        rangeMin2dim.resize(2 * dim);
        rangeMax2dim.resize(2 * dim);
        List<Scalar> point = {node.X(), node.Y(), node.Z()};

        // 将单元中心转换为2*dim空间中的范围
        for (int i = 0; i < dim; i++)
        {
            rangeMin2dim[i] = -1 * INF;
            rangeMin2dim[i + dim] = point[i];

            rangeMax2dim[i] = point[i];
            rangeMax2dim[i + dim] = INF;
        }
    }

    void OversetDonorSearcher::SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults)
    {
        // 合并所有搜索结果
        mergedResults.clear();
        for (int i = 0; i < groupedSearchResults.size(); i++)
        {
            mergedResults.insert(mergedResults.end(), groupedSearchResults[i].begin(), groupedSearchResults[i].end());
        }

        if (mergedResults.size() == 0)
        {
            return;
        }

        // 排序（按照Acceptor的比较规则）
        std::sort(mergedResults.begin(), mergedResults.end(), Acceptor::cmp);

        // 筛选重复元素，保留最优贡献单元
        for (auto it = mergedResults.begin() + 1; it != mergedResults.end();)
        {
            // 当前元素与前一元素重复，更新前一元素信息，删除当前元素
            if (it->GetAcceptorID() == (it - 1)->GetAcceptorID())
            {
                (it - 1)->SetCentralDonor(it->GetCentralDonorID(),
                                          it->GetCentralDonorProcID(),
                                          it->GetCentralDonorVolume(),
                                          it->GetCentralDonorType());
                mergedResults.erase(it);
            }
            else // 当前元素与前一元素不重复，迭代器+1指向后一元素
            {
                it++;
            }
        }
    }

    bool OversetDonorSearcher::NodeInElem(const Node &node, const Element &elem)
    {
        // 简化的点在单元内判断
        // 实际实现需要根据单元类型（三角形、四边形、四面体、六面体等）进行具体判断
        // 这里提供一个基础的包围盒判断作为示例

        const Vector &elemCenter = elem.GetCenter();
        Scalar maxDist = 0.0;

        // 计算单元的特征尺寸
        for (int nodeI = 0; nodeI < elem.GetNodeSize(); nodeI++)
        {
            const int &nodeID = elem.GetNodeID(nodeI);
            const Vector &elemNode = localMesh->GetNode(nodeID);
            Scalar dist = (elemNode - elemCenter).Mag();
            maxDist = std::max(maxDist, dist);
        }

        // 简单的距离判断：如果查询点到单元中心的距离小于单元特征尺寸，认为在单元内
        Scalar queryDist = (node - elemCenter).Mag();
        return queryDist <= maxDist;

        // TODO: 实现更精确的点在单元内判断算法
        // 对于不同的单元类型需要使用不同的算法：
        // - 三角形：重心坐标法
        // - 四边形：双线性插值法
        // - 四面体：重心坐标法
        // - 六面体：三线性插值法
    }

} // namespace Overset
