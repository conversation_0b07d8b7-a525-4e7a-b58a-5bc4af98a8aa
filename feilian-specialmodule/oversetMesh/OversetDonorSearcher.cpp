#include "feilian-specialmodule/oversetMesh/OversetDonorSearcher.h"

namespace Overset
{

    OversetDonorSearcher::OversetDonorSearcher(Mesh *mesh_,
                                               ZoneManager *zoneManager_,
                                               ElementField<int> *elemTypeField_,
                                               const boost::mpi::communicator &mpi_world_)
        : localMesh(mesh_), zoneManager(zoneManager_), elemTypeField(elemTypeField_), mpi_world(mpi_world_), processorID(mpi_world_.rank()), nProcessor(mpi_world_.size())
    {
        n_Zones = zoneManager->GetZoneNumber();
        dim = localMesh->GetMeshDim();
    }

    OversetDonorSearcher::~OversetDonorSearcher()
    {
        Clear();
    }

    void OversetDonorSearcher::Initialize()
    {
        // 清理之前的数据
        Clear();

        // 为每个子域创建KDT搜索器
        kdtSearchers.resize(n_Zones);
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            kdtSearchers[zoneID] = new KDT(dim);

            // 为当前子域创建单元KDT树
            int zoneElemStartID = zoneManager->GetZoneStartElemID(zoneID);
            int zoneElemNum = zoneManager->GetZoneElemNum(zoneID);

            if (zoneElemNum > 0)
            {
                // 创建子域网格
                Mesh *zoneMesh = CreateZoneMesh(zoneID);
                kdtSearchers[zoneID]->CreateElementKDTree(zoneMesh);
                delete zoneMesh; // 清理临时网格
            }
        }

        // 初始化贡献单元标记
        acceptorDonorFlag.assign(localMesh->GetElementNumberReal(), 0);
    }

    bool OversetDonorSearcher::ElemCenterInTree(const int &elemID, const TreeInfo &treeInfo)
    {
        const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
        List<Scalar> center = {elemCenter.X(), elemCenter.Y(), elemCenter.Z()};
        for (int i = 0; i < dim; i++)
        {
            if (center[i] < treeInfo.spaceMin[i] || center[i] > treeInfo.spaceMax[i + dim])
            {
                return false;
            }
        }
        return true;
    }

    void OversetDonorSearcher::GroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcpts)
    {
        groupedAcpts.clear();
        groupedAcpts.resize(nProcessor);

        for (auto it = searchElemID.begin(); it != searchElemID.end(); it++)
        {
            int elemID = *it;
            int elemZoneID = zoneManager->GetElemZoneID(elemID);

            bool grouped = false;
            for (int treeI = 0; treeI < globalTreeInfo.size(); treeI++)
            {
                TreeInfo &treeInfo = globalTreeInfo[treeI];

                if (elemZoneID != treeInfo.zoneID) // 只搜索其他子域
                {
                    // 判断单元是否与该树空间相交
                    if (ElemCenterInTree(elemID, treeInfo))
                    {
                        const Node &center = this->localMesh->GetElement(elemID).GetCenter();
                        Acceptor temp(elemID, processorID, elemZoneID, center);
                        groupedAcpts[treeInfo.procID].push_back(temp);
                        grouped = true;
                    }
                }
            }

            if (grouped == false) // 无法分配到任何树（无贡献单元），加入到当前进程
            {
                const Node &center = this->localMesh->GetElement(elemID).GetCenter();
                Acceptor temp(elemID, processorID, elemZoneID, center);
                groupedAcpts[processorID].push_back(temp);
            }
        }
        searchElemID.clear();
    }

    void OversetDonorSearcher::Clear()
    {
        // 清理KDT搜索器
        for (int zoneID = 0; zoneID < kdtSearchers.size(); zoneID++)
        {
            delete kdtSearchers[zoneID];
        }
        kdtSearchers.clear();

        // 清理数据容器
        acceptorDonorFlag.clear();
    }

    void OversetDonorSearcher::ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults)
    {
        // 简化的并行搜索实现
        // 处理本进程内的搜索
        ChunkDonorSearch(groupedAcpts[processorID]);

        // 将本进程的搜索结果加入结果集
        searchResults.insert(groupedAcpts[processorID].begin(), groupedAcpts[processorID].end());

        // 实际的并行实现需要包含MPI通信逻辑
        // 这里暂时只处理本进程的搜索，避免程序崩溃
        if (nProcessor > 1)
        {
            // TODO: 实现完整的MPI并行搜索逻辑
            // 包括：发送搜索请求、接收搜索结果、合并结果等
        }
    }

    void OversetDonorSearcher::ChunkDonorSearch(List<Acceptor> &acptList)
    {
        if (acptList.size() == 0)
        {
            return;
        }

        for (int i = 0; i < acptList.size(); i++)
        {
            Acceptor &acpt = acptList[i];
            const Node &elemCenter = acpt.GetAcceptorCenter();
            const int &donorID = acpt.GetCentralDonorID();
            const int &donorProcID = acpt.GetCentralDonorProcID();

            if (donorID >= 0 && donorProcID == processorID) // 如果已经有贡献单元且就在本进程
            {
                const Element &elem = this->localMesh->GetElement(donorID);
                if (this->NodeInElem(elemCenter, elem)) // 且插值单元中心仍然在原来的贡献单元里面
                {
                    const int &newDonorType = elemTypeField->GetValue(donorID);
                    const Scalar &newDonorVolume = elem.GetVolume();
                    acpt.SetCentralDonor(donorID, donorProcID, newDonorVolume, newDonorType);
                    continue;
                }
            }

            // 在各个子域的KDT搜索器中搜索贡献单元
            for (int zoneI = 0; zoneI < kdtSearchers.size(); zoneI++)
            {
                if (kdtSearchers[zoneI] && acpt.GetAcceptorZoneID() != zoneI) // 仅搜索其他子域
                {
                    int newDonorID = this->DonorSearchWithKDT(elemCenter, zoneI);
                    if (newDonorID >= 0)
                    {
                        Scalar elemVolume = localMesh->GetElement(newDonorID).GetVolume();
                        acpt.SetCentralDonor(newDonorID,
                                             processorID,
                                             elemVolume,
                                             elemTypeField->GetValue(newDonorID));
                        break; // 找到贡献单元后跳出循环
                    }
                }
            }
        }
    }

    int OversetDonorSearcher::DonorSearch(const Node &srcNode, KdtTree *tgtTree)
    {
        // 这个方法签名保持兼容性，但实际使用KDT类的接口
        // 在实际调用中应该使用DonorSearchWithKDT方法
        FatalError("OversetDonorSearcher::DonorSearch() 已废弃，请使用DonorSearchWithKDT()");
        return -1;
    }

    int OversetDonorSearcher::DonorSearchWithKDT(const Node &srcNode, int zoneID)
    {
        if (zoneID < 0 || zoneID >= kdtSearchers.size() || kdtSearchers[zoneID] == nullptr)
        {
            return -1;
        }

        int donorID = -1;
        kdtSearchers[zoneID]->SearchDonorForTgtnode(srcNode, donorID);

        if (donorID >= 0)
        {
            const int &elemType = elemTypeField->GetValue(donorID);
            if (elemType == OversetElemType::ACCEPTOR) // 贡献单元作为了插值单元，标记出来
            {
                acceptorDonorFlag[donorID] = 1;
            }
        }

        return donorID;
    }

    void OversetDonorSearcher::GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts)
    {
        groupedAcpts.clear();
        groupedAcpts.resize(nProcessor);

        for (auto it = srcAcpts.begin(); it != srcAcpts.end(); it++)
        {
            const int &donorProcID = it->GetCentralDonorProcID();
            const int &donorID = it->GetCentralDonorID();

            if (donorID >= 0)
            {
                groupedAcpts[donorProcID].push_back(*it);
            }
            else
            {
                int elemID = it->GetAcceptorID();
                int elemZoneID = zoneManager->GetElemZoneID(elemID);

                bool grouped = false;
                const std::vector<TreeInfo> &globalTreeInfo = GetGlobalTreeInfo();
                for (int treeI = 0; treeI < globalTreeInfo.size(); treeI++)
                {
                    const TreeInfo &treeInfo = globalTreeInfo[treeI];

                    if (elemZoneID != treeInfo.zoneID) // 只搜索其他子域
                    {
                        // 判断单元是否与该树空间相交
                        if (ElemCenterInTree(elemID, treeInfo))
                        {
                            groupedAcpts[treeInfo.procID].push_back(*it);
                            grouped = true;
                        }
                    }
                }

                if (grouped == false) // 无法分配到任何树（无贡献单元），加入到当前进程
                {
                    groupedAcpts[processorID].push_back(*it);
                }
            }
        }
    }

    void OversetDonorSearcher::SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults)
    {
        // 合并所有搜索结果
        mergedResults.clear();
        for (int i = 0; i < groupedSearchResults.size(); i++)
        {
            mergedResults.insert(mergedResults.end(), groupedSearchResults[i].begin(), groupedSearchResults[i].end());
        }

        if (mergedResults.size() == 0)
        {
            return;
        }

        // 排序（按照Acceptor的比较规则）
        std::sort(mergedResults.begin(), mergedResults.end(), Acceptor::cmp);

        // 筛选重复元素，保留最优贡献单元
        for (auto it = mergedResults.begin() + 1; it != mergedResults.end();)
        {
            // 当前元素与前一元素重复，更新前一元素信息，删除当前元素
            if (it->GetAcceptorID() == (it - 1)->GetAcceptorID())
            {
                (it - 1)->SetCentralDonor(it->GetCentralDonorID(),
                                          it->GetCentralDonorProcID(),
                                          it->GetCentralDonorVolume(),
                                          it->GetCentralDonorType());
                mergedResults.erase(it);
            }
            else // 当前元素与前一元素不重复，迭代器+1指向后一元素
            {
                it++;
            }
        }
    }

    bool OversetDonorSearcher::NodeInElem(const Node &node, const Element &elem)
    {
        // 使用KDT类的IsNodeInElement方法进行精确判断
        // 这个方法已经实现了对不同单元类型的精确判断算法

        // 创建临时KDT实例进行判断
        KDT tempKDT(dim);
        return tempKDT.IsNodeInElement(node, elem, localMesh);
    }

    const std::vector<TreeInfo> &OversetDonorSearcher::GetGlobalTreeInfo() const
    {
        // 收集所有KDT搜索器的全局树信息
        static std::vector<TreeInfo> globalTreeInfo;
        globalTreeInfo.clear();

        for (int zoneID = 0; zoneID < kdtSearchers.size(); zoneID++)
        {
            if (kdtSearchers[zoneID] != nullptr)
            {
                const std::vector<TreeInfo> &zoneTreeInfo = kdtSearchers[zoneID]->GetGlobalTreeInfo();
                globalTreeInfo.insert(globalTreeInfo.end(), zoneTreeInfo.begin(), zoneTreeInfo.end());
            }
        }

        return globalTreeInfo;
    }

    Mesh *OversetDonorSearcher::CreateZoneMesh(int zoneID)
    {
        // 创建子域网格的简化实现
        // 实际实现中需要根据zoneID提取对应的子域网格
        // 这里暂时返回整个网格，KDT类会自动处理子域范围
        return localMesh;
    }

} // namespace Overset
