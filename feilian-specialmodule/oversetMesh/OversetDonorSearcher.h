////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetDonorSearcher.h
//! <AUTHOR>
//! @brief 重叠网格贡献单元搜索器
//! @date 2024-12-19
//
//------------------------------修改日志----------------------------------------
// 2024-12-19 曾凯
//    说明：从OversetMesh类中分离贡献单元搜索功能，提高代码模块化程度
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetDonorSearcher_
#define _specialModule_oversetMesh_OversetDonorSearcher_

#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "meshProcess/zone/ZoneManager.h"
#include "meshProcess/wallDistance/KDT_utilities.h"
#include "basic/mesh/Mesh.h"
#include "basic/dataStruct/List.h"
#include "basic/dataStruct/Set.h"
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/mpi.hpp"

namespace Overset
{
    /**
     * @brief 重叠网格贡献单元搜索器
     *
     * 负责为插值单元搜索合适的贡献单元，支持KDT树加速和并行搜索
     */
    class OversetDonorSearcher
    {
    public:
        /**
         * @brief 构造函数
         *
         * @param mesh_ 网格指针
         * @param zoneManager_ 域管理器指针
         * @param elemTypeField_ 单元类型场指针
         * @param mpi_world_ MPI通信器
         */
        OversetDonorSearcher(Mesh *mesh_,
                             ZoneManager *zoneManager_,
                             ElementField<int> *elemTypeField_,
                             const boost::mpi::communicator &mpi_world_);

        /**
         * @brief 析构函数
         */
        ~OversetDonorSearcher();

        /**
         * @brief 初始化贡献单元搜索器
         */
        void Initialize();

        /**
         * @brief 为批量插值单元并行搜索贡献单元
         *
         * @param groupedAcpts 按进程分组的插值单元列表
         * @param searchResults 搜索结果
         */
        void ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults);

        /**
         * @brief 为单个插值单元列表搜索贡献单元
         *
         * @param acptList 插值单元列表
         */
        void ChunkDonorSearch(List<Acceptor> &acptList);

        /**
         * @brief 为单个点搜索贡献单元（兼容性接口，已废弃）
         *
         * @param srcNode 插值点坐标
         * @param tgtTree 目标KDT树
         * @return int 贡献单元编号，未找到时返回-1
         */
        int DonorSearch(const Node &srcNode, KdtTree *tgtTree);

        /**
         * @brief 为单个点搜索贡献单元（使用KDT类）
         *
         * @param srcNode 插值点坐标
         * @param zoneID 目标子域编号
         * @return int 贡献单元编号，未找到时返回-1
         */
        int DonorSearchWithKDT(const Node &srcNode, int zoneID);

        /**
         * @brief 将插值单元按照可能存在贡献单元的进程分组
         *
         * @param searchElemID 需要搜索贡献单元的单元编号集合
         * @param groupedAcceptors 分组结果
         */
        void GroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcceptors);

        /**
         * @brief 将插值单元按照贡献单元的进程号分组
         *
         * @param srcAcpts 源插值单元集合
         * @param groupedAcpts 分组结果
         */
        void GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts);

        /**
         * @brief 判断指定单元是否可能与目标树相交
         *
         * @param elemID 单元编号
         * @param treeInfo 树信息
         * @return true 可能相交
         * @return false 不相交
         */
        bool ElemCenterInTree(const int &elemID, const TreeInfo &treeInfo);

        /**
         * @brief 获取全局树信息（只读访问）
         *
         * @return const std::vector<TreeInfo>& 全局树信息
         */
        const std::vector<TreeInfo> &GetGlobalTreeInfo() const;

        /**
         * @brief 获取贡献单元标记数组（只读访问）
         *
         * @return const List<int>& 贡献单元标记数组
         */
        const List<int> &GetAcceptorDonorFlag() const { return acceptorDonorFlag; }

        /**
         * @brief 清理搜索数据
         */
        void Clear();

    private:
        /**
         * @brief 合并各进程搜索结果，选择最优贡献单元
         *
         * @param groupedSearchResults 分组搜索结果
         * @param mergedResults 合并后的结果
         */
        void SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults);

        /**
         * @brief 判断点是否在网格单元内部
         *
         * @param node 坐标点
         * @param elem 网格单元
         * @return true 在内部
         * @return false 在外部
         */
        bool NodeInElem(const Node &node, const Element &elem);

        /**
         * @brief 创建指定子域的网格
         *
         * @param zoneID 子域编号
         * @return Mesh* 子域网格指针
         */
        Mesh *CreateZoneMesh(int zoneID);

    private:
        // 基础数据
        Mesh *localMesh;                           // 当前进程网格指针
        ZoneManager *zoneManager;                  // 域管理器指针
        ElementField<int> *elemTypeField;          // 网格单元重叠类型场
        const boost::mpi::communicator &mpi_world; // MPI通信器
        int n_Zones;                               // 网格子域个数
        Mesh::MeshDim dim;                         // 网格维度
        int processorID;                           // 当前进程ID
        int nProcessor;                            // 总进程数

        // KDT搜索器
        List<KDT *> kdtSearchers; // 各子域的KDT搜索器

        // 搜索状态
        List<int> acceptorDonorFlag; // 用于标记插值单元是否被用作了贡献单元
    };
}

#endif
