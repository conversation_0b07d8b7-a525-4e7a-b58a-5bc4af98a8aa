#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include "feilian-specialmodule/oversetMesh/OversetDonorSearcher.h"

namespace Overset
{

    OversetWallDistanceCalculator::OversetWallDistanceCalculator(Mesh *mesh_,
                                                                 ZoneManager *zoneManager_,
                                                                 const boost::mpi::communicator &mpi_world_)
        : localMesh(mesh_), zoneManager(zoneManager_), mpi_world(mpi_world_), processorID(mpi_world_.rank()), nProcessor(mpi_world_.size())
    {
        n_Zones = zoneManager->GetZoneNumber();
        dim = localMesh->GetMeshDim();
    }

    OversetWallDistanceCalculator::~OversetWallDistanceCalculator()
    {
        Clear();
    }

    void OversetWallDistanceCalculator::Initialize(Turbulence::WallDistance wallDistMethod_,
                                                   ElemTypeMethod elemTypeMethod_)
    {
        wallDistMethod = wallDistMethod_;
        elemTypeMethod = elemTypeMethod_;

        // 清理之前的数据
        Clear();

        // 收集全局壁面数据
        CollectGlobalWallFaces();

        // 创建壁面KDT树
        if (wallDistMethod == Turbulence::WallDistance::KDT)
        {
            globalWallFaceKdtTrees.resize(n_Zones);
            for (int zoneID = 0; zoneID < n_Zones; zoneID++)
            {
                globalWallFaceKdtTrees[zoneID] = CreateFaceKdtTree(globalWallFaces[zoneID]);
            }
        }
    }

    void OversetWallDistanceCalculator::CalculateWallDistances(OversetDonorSearcher *donorSearcher)
    {
        wallDistances.resize(n_Zones);
        nearestWallFaceID.resize(n_Zones);

        if (wallDistMethod == Turbulence::WallDistance::KDT)
        {
            // 计算壁面距离
            CalculateWallDistByKDT();

            // 修正壁面距离（需要贡献单元搜索器支持）
            if (donorSearcher != nullptr)
            {
                CorrectWallDist(donorSearcher);
            }
        }
        else
        {
            FatalError("OversetWallDistanceCalculator: 暂不支持该壁面距离计算方法");
        }
    }

    void OversetWallDistanceCalculator::CalculateWallDistByKDT()
    {
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            // 子域网格中不存在壁面时，将所有网格对该子域的壁面距离设为极大值
            if (globalWallFaces[zoneID].size() == 0)
            {
                wallDistances[zoneID].resize(localMesh->GetElementNumberReal(), INF);
            }
            else
            {
                List<Scalar> temp(3, INF);
                Pair<Scalar, int> nearest;

                switch (elemTypeMethod)
                {
                case ElemTypeMethod::ElemBased:
                    wallDistances[zoneID].resize(localMesh->GetElementNumberReal());
                    nearestWallFaceID[zoneID].resize(localMesh->GetElementNumberReal());
                    for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
                    {
                        const Vector &node = localMesh->GetElement(elemID).GetCenter();
                        temp[0] = node.X();
                        temp[1] = node.Y();
                        temp[2] = node.Z();
                        nearest = globalWallFaceKdtTrees[zoneID]->FindNearestNeighbour(temp);
                        wallDistances[zoneID][elemID] = nearest.first;
                        nearestWallFaceID[zoneID][elemID] = nearest.second;
                    }
                    break;

                case ElemTypeMethod::NodeBased:
                    wallDistances[zoneID].resize(localMesh->GetNodeNumber());
                    nearestWallFaceID[zoneID].resize(localMesh->GetNodeNumber());
                    for (int nodeID = 0; nodeID < localMesh->GetNodeNumber(); nodeID++)
                    {
                        const Vector &node = localMesh->GetNode(nodeID);
                        temp = {node.X(), node.Y(), node.Z()};
                        nearest = globalWallFaceKdtTrees[zoneID]->FindNearestNeighbour(temp);
                        wallDistances[zoneID][nodeID] = nearest.first;
                        nearestWallFaceID[zoneID][nodeID] = nearest.second;
                    }
                    break;

                default:
                    break;
                }
            }
        }
    }

    void OversetWallDistanceCalculator::UpdateWallDistField()
    {
        switch (elemTypeMethod)
        {
        case ElemTypeMethod::ElemBased:
        {
            Scalar minDist;
            for (int elemID = 0; elemID < localMesh->n_elemNum; elemID++)
            {
                minDist = INF;
                for (int zoneID = 0; zoneID < n_Zones; zoneID++)
                {
                    minDist = Min(minDist, wallDistances[zoneID][elemID]);
                }
                localMesh->v_nearWallDistance[elemID] = minDist;
            }
        }
        break;

        case ElemTypeMethod::NodeBased:
            for (int zoneID = 0; zoneID < n_Zones; zoneID++)
            {
                for (int elemID = 0; elemID < localMesh->n_elemNum; elemID++)
                {
                    Node elemCenter = localMesh->v_elem[elemID].GetCenter();
                    std::vector<Scalar> temp = {elemCenter.X(), elemCenter.Y(), elemCenter.Z()};
                    std::pair<Scalar, int> nearest = globalWallFaceKdtTrees[zoneID]->FindNearestNeighbour(temp);
                    if (nearest.first < localMesh->v_nearWallDistance[elemID])
                    {
                        localMesh->v_nearWallDistance[elemID] = nearest.first;
                    }
                }
            }
            break;

        default:
            break;
        }
    }

    Scalar OversetWallDistanceCalculator::GetWallDistance(int elemID, int zoneID) const
    {
        if (zoneID >= 0 && zoneID < wallDistances.size() &&
            elemID >= 0 && elemID < wallDistances[zoneID].size())
        {
            return wallDistances[zoneID][elemID];
        }
        return INF;
    }

    bool OversetWallDistanceCalculator::IsNearestWallDistToSelf(int elemID, int elemZoneID) const
    {
        for (int otherZoneID = 0; otherZoneID < n_Zones; otherZoneID++)
        {
            if (otherZoneID != elemZoneID &&
                wallDistances[elemZoneID][elemID] > wallDistances[otherZoneID][elemID])
            {
                return false;
            }
        }
        return true;
    }

    void OversetWallDistanceCalculator::Clear()
    {
        // 清理KDT树
        for (int zoneID = 0; zoneID < globalWallFaceKdtTrees.size(); zoneID++)
        {
            delete globalWallFaceKdtTrees[zoneID];
        }
        globalWallFaceKdtTrees.clear();

        // 清理数据容器
        globalWallFaces.clear();
        globalWallFaceNodes.clear();
        wallDistances.clear();
        nearestWallFaceID.clear();
    }

    void OversetWallDistanceCalculator::CollectGlobalWallFaces()
    {
        // 收集全局壁面边界面元列表
        globalWallFaces.resize(n_Zones);
        globalWallFaceNodes.resize(n_Zones);

        // 这里需要调用具体的边界面元收集方法
        // 由于涉及到Boundary::Type::WALL的处理，需要访问网格的边界信息
        // 暂时作为接口预留，实际实现时需要根据feilian框架的具体API来实现

        // 示例实现框架：
        // for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        // {
        //     // 收集指定子域的壁面边界面元
        //     CollectZoneWallFaces(zoneID, globalWallFaces[zoneID], globalWallFaceNodes[zoneID]);
        // }

        // 临时实现：初始化为空，避免程序崩溃
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            globalWallFaces[zoneID].clear();
            globalWallFaceNodes[zoneID].clear();
        }
    }

    KdtTree *OversetWallDistanceCalculator::CreateFaceKdtTree(const List<Face> &faceList)
    {
        if (faceList.empty())
        {
            return nullptr;
        }

        // 创建面元中心点云和数据云
        List<List<Scalar>> pointCloud;
        List<int> dataCloud;

        for (int faceI = 0; faceI < faceList.size(); faceI++)
        {
            const Face &face = faceList[faceI];
            const Vector &center = face.GetCenter();
            List<Scalar> point = {center.X(), center.Y(), center.Z()};
            pointCloud.push_back(point);
            dataCloud.push_back(faceI);
        }

        // 创建KDT树
        return new KdtTree(dim, pointCloud, dataCloud);
    }

    void OversetWallDistanceCalculator::CorrectWallDist(OversetDonorSearcher *donorSearcher)
    {
        if (donorSearcher == nullptr)
        {
            return; // 没有贡献单元搜索器，跳过修正
        }

        List<List<int>> needDonorSearchElem;
        needDonorSearchElem.resize(n_Zones);

        // 第一步：识别需要修正的单元
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
        {
            const int elemZoneID = zoneManager->GetElemZoneID(elemID);
            for (int zoneI = 0; zoneI < n_Zones; zoneI++)
            {
                const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
                Scalar &wallDist = wallDistances[zoneI][elemID];

                if (zoneI < nearestWallFaceID.size() && elemID < nearestWallFaceID[zoneI].size())
                {
                    int &wallFaceID = nearestWallFaceID[zoneI][elemID];

                    if (wallFaceID >= 0 && wallFaceID < globalWallFaces[zoneI].size())
                    {
                        const Vector &wallFaceNormal = globalWallFaces[zoneI][wallFaceID].GetNormal();
                        const Vector &wallFaceCenter = globalWallFaces[zoneI][wallFaceID].GetCenter();
                        const auto &nodeList = globalWallFaceNodes[zoneI][wallFaceID];

                        Vector elemToFace = wallFaceCenter - elemCenter;
                        Vector unitWallFaceNormal = wallFaceNormal / wallFaceNormal.Mag();
                        Scalar normalDist = elemToFace & unitWallFaceNormal;

                        // 进行几何修正（简化版本）
                        if (normalDist < 0) // 需要进一步判断
                        {
                            if (elemZoneID == zoneI) // 是到自身子域的壁面，修正为正
                            {
                                wallDist = abs(wallDist);
                            }
                            else // 是到其他子域的壁面，需要通过贡献单元搜索判断
                            {
                                needDonorSearchElem[zoneI].push_back(elemID);
                            }
                        }
                    }
                }
            }
        }

        // 第二步：通过贡献单元搜索进行修正
        // 这部分需要与贡献单元搜索器协作，暂时简化实现
        // 实际实现中需要调用donorSearcher的相关方法
    }

} // namespace Overset
